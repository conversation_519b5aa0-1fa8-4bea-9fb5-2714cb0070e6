<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>店铺信息管理 - TeleShop</title>
    <link rel="stylesheet" href="../styles/supplier-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .store-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 24px;
        }

        .store-header {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .store-preview {
            display: flex;
            gap: 24px;
            align-items: center;
            margin-bottom: 24px;
        }

        .store-logo {
            width: 80px;
            height: 80px;
            border-radius: 12px;
            object-fit: cover;
            border: 3px solid #e0e0e0;
        }

        .store-basic-info h2 {
            margin-bottom: 8px;
            color: #212121;
        }

        .store-meta {
            display: flex;
            gap: 16px;
            color: #757575;
            font-size: 14px;
        }

        .form-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #212121;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-label {
            font-weight: 500;
            color: #212121;
            font-size: 14px;
        }

        .required::after {
            content: '*';
            color: #f44336;
            margin-left: 4px;
        }

        .form-input {
            padding: 12px 16px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #2196F3;
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
        }

        .logo-upload {
            display: flex;
            gap: 20px;
            align-items: start;
        }

        .logo-preview {
            width: 120px;
            height: 120px;
            border: 2px dashed #e0e0e0;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fafafa;
        }

        .logo-preview:hover {
            border-color: #2196F3;
            background: #f3f9ff;
        }

        .logo-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 10px;
        }

        .upload-text {
            text-align: center;
            color: #757575;
            font-size: 12px;
        }

        .business-hours {
            display: grid;
            gap: 12px;
        }

        .hour-row {
            display: grid;
            grid-template-columns: 100px 1fr 1fr 100px;
            gap: 12px;
            align-items: center;
        }

        .day-label {
            font-weight: 500;
            color: #212121;
        }

        .time-input {
            padding: 8px 12px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
        }

        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: #e0e0e0;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .toggle-switch.active {
            background: #4CAF50;
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        .toggle-switch.active::after {
            transform: translateX(20px);
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 16px;
            background: #f9f9f9;
            border-radius: 8px;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #2196F3;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #757575;
        }

        .action-buttons {
            display: flex;
            gap: 16px;
            justify-content: flex-end;
            padding-top: 24px;
            border-top: 1px solid #e0e0e0;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #2196F3;
            color: white;
        }

        .btn-primary:hover {
            background: #1976D2;
        }

        .btn-secondary {
            background: #f5f5f5;
            color: #757575;
            border: 1px solid #e0e0e0;
        }

        .btn-secondary:hover {
            background: #eeeeee;
        }
    </style>
</head>
<body>
    <div class="supplier-dashboard">
        <header class="dashboard-header">
            <div class="header-content">
                <div class="logo-section">
                    <img src="../../admin-backend/assets/images/logo.png" alt="TeleShop" class="logo">
                    <h1>店铺信息管理</h1>
                </div>
                <div class="header-actions">
                    <a href="../index.html" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回首页
                    </a>
                </div>
            </div>
        </header>

        <div class="dashboard-main">
            <main class="main-content">
                <div class="store-container">
                    <!-- 店铺预览 -->
                    <div class="store-header">
                        <div class="store-preview">
                            <img src="../../admin-backend/assets/images/product-default.jpg" alt="店铺Logo" class="store-logo">
                            <div class="store-basic-info">
                                <h2>优质电子产品专营店</h2>
                                <div class="store-meta">
                                    <span><i class="fas fa-star"></i> 4.8分</span>
                                    <span><i class="fas fa-users"></i> 1.2万关注</span>
                                    <span><i class="fas fa-calendar"></i> 开店2年</span>
                                    <span><i class="fas fa-check-circle" style="color: #4CAF50;"></i> 已认证</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">358</div>
                                <div class="stat-label">在售商品</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">2.8万</div>
                                <div class="stat-label">总销量</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">98.5%</div>
                                <div class="stat-label">好评率</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">12小时</div>
                                <div class="stat-label">平均发货</div>
                            </div>
                        </div>
                    </div>

                    <form id="storeInfoForm">
                        <!-- 基础信息 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-info-circle"></i>
                                基础信息
                            </h3>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label class="form-label required">店铺名称</label>
                                    <input type="text" class="form-input" name="storeName" value="优质电子产品专营店" required>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">店铺简称</label>
                                    <input type="text" class="form-input" name="storeShortName" value="优质电子">
                                </div>
                                <div class="form-group">
                                    <label class="form-label required">主营类目</label>
                                    <select class="form-input" name="mainCategory" required>
                                        <option value="electronics" selected>数码电子</option>
                                        <option value="clothing">服装鞋帽</option>
                                        <option value="home">家居用品</option>
                                        <option value="beauty">美妆护肤</option>
                                        <option value="food">食品饮料</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">店铺等级</label>
                                    <input type="text" class="form-input" value="金牌商家" readonly>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label required">店铺介绍</label>
                                <textarea class="form-input" name="storeDescription" rows="4" required>专注于高品质电子产品销售，提供原装正品，享受完善售后服务。我们致力于为客户提供最优质的购物体验。</textarea>
                            </div>

                            <div class="form-group">
                                <label class="form-label">店铺Logo</label>
                                <div class="logo-upload">
                                    <div class="logo-preview" onclick="triggerFileUpload('storeLogo')">
                                        <img src="../../admin-backend/assets/images/product-default.jpg" alt="当前Logo" id="logoPreview">
                                    </div>
                                    <div>
                                        <input type="file" id="storeLogo" name="storeLogo" accept=".jpg,.jpeg,.png" style="display: none;" onchange="previewLogo(this)">
                                        <div class="upload-text">
                                            <p><strong>Logo要求：</strong></p>
                                            <p>• 尺寸：建议200x200px</p>
                                            <p>• 格式：JPG、PNG</p>
                                            <p>• 大小：不超过2MB</p>
                                            <p>• 内容：清晰的品牌标识</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 联系信息 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-address-book"></i>
                                联系信息
                            </h3>
                            <div class="contact-grid">
                                <div class="form-group">
                                    <label class="form-label required">客服电话</label>
                                    <input type="tel" class="form-input" name="servicePhone" value="************" required>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">客服邮箱</label>
                                    <input type="email" class="form-input" name="serviceEmail" value="<EMAIL>">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">客服QQ</label>
                                    <input type="text" class="form-input" name="serviceQQ" value="123456789">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">微信客服</label>
                                    <input type="text" class="form-input" name="serviceWechat" value="teleshop_service">
                                </div>
                            </div>
                        </div>

                        <!-- 资质认证 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-certificate"></i>
                                资质认证
                            </h3>
                            <div class="form-group">
                                <label class="form-label required">营业执照</label>
                                <div class="upload-area" onclick="triggerFileUpload('businessLicense')">
                                    <div class="upload-icon">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                    </div>
                                    <div class="upload-text">
                                        点击上传营业执照副本（支持 JPG、PNG、PDF 格式，不超过5MB）
                                    </div>
                                </div>
                                <input type="file" id="businessLicense" name="businessLicense" accept=".jpg,.jpeg,.png,.pdf" style="display: none;" required>
                                <div id="businessLicenseList" class="file-list"></div>
                            </div>

                            <div class="form-group">
                                <label class="form-label required">法人身份证</label>
                                <div class="upload-area" onclick="triggerFileUpload('legalIdCard')">
                                    <div class="upload-icon">
                                        <i class="fas fa-id-card"></i>
                                    </div>
                                    <div class="upload-text">
                                        请上传法人身份证正反面（支持 JPG、PNG 格式，不超过3MB）
                                    </div>
                                </div>
                                <input type="file" id="legalIdCard" name="legalIdCard" accept=".jpg,.jpeg,.png" multiple style="display: none;" required>
                                <div id="legalIdCardList" class="file-list"></div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">行业许可证</label>
                                <div class="upload-area" onclick="triggerFileUpload('industryLicense')">
                                    <div class="upload-icon">
                                        <i class="fas fa-file-certificate"></i>
                                    </div>
                                    <div class="upload-text">
                                        如涉及特殊行业，请上传相关许可证（可选）
                                    </div>
                                </div>
                                <input type="file" id="industryLicense" name="industryLicense" accept=".jpg,.jpeg,.png,.pdf" multiple style="display: none;">
                                <div id="industryLicenseList" class="file-list"></div>
                            </div>
                        </div>

                        <!-- 营业时间 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-clock"></i>
                                营业时间
                            </h3>
                            <div class="business-hours">
                                <div class="hour-row">
                                    <div class="day-label">周一</div>
                                    <input type="time" class="time-input" name="mon_start" value="09:00">
                                    <input type="time" class="time-input" name="mon_end" value="18:00">
                                    <div class="toggle-switch active" onclick="toggleDay(this)"></div>
                                </div>
                                <div class="hour-row">
                                    <div class="day-label">周二</div>
                                    <input type="time" class="time-input" name="tue_start" value="09:00">
                                    <input type="time" class="time-input" name="tue_end" value="18:00">
                                    <div class="toggle-switch active" onclick="toggleDay(this)"></div>
                                </div>
                                <div class="hour-row">
                                    <div class="day-label">周三</div>
                                    <input type="time" class="time-input" name="wed_start" value="09:00">
                                    <input type="time" class="time-input" name="wed_end" value="18:00">
                                    <div class="toggle-switch active" onclick="toggleDay(this)"></div>
                                </div>
                                <div class="hour-row">
                                    <div class="day-label">周四</div>
                                    <input type="time" class="time-input" name="thu_start" value="09:00">
                                    <input type="time" class="time-input" name="thu_end" value="18:00">
                                    <div class="toggle-switch active" onclick="toggleDay(this)"></div>
                                </div>
                                <div class="hour-row">
                                    <div class="day-label">周五</div>
                                    <input type="time" class="time-input" name="fri_start" value="09:00">
                                    <input type="time" class="time-input" name="fri_end" value="18:00">
                                    <div class="toggle-switch active" onclick="toggleDay(this)"></div>
                                </div>
                                <div class="hour-row">
                                    <div class="day-label">周六</div>
                                    <input type="time" class="time-input" name="sat_start" value="10:00">
                                    <input type="time" class="time-input" name="sat_end" value="17:00">
                                    <div class="toggle-switch active" onclick="toggleDay(this)"></div>
                                </div>
                                <div class="hour-row">
                                    <div class="day-label">周日</div>
                                    <input type="time" class="time-input" name="sun_start" value="10:00">
                                    <input type="time" class="time-input" name="sun_end" value="17:00">
                                    <div class="toggle-switch" onclick="toggleDay(this)"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="action-buttons">
                            <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                <i class="fas fa-undo"></i> 重置
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="saveAsDraft()">
                                <i class="fas fa-save"></i> 保存草稿
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-check"></i> 保存设置
                            </button>
                        </div>
                    </form>
                </div>
            </main>
        </div>
    </div>

    <script>
        // 文件上传功能
        function triggerFileUpload(inputId) {
            document.getElementById(inputId).click();
        }

        // 预览Logo
        function previewLogo(input) {
            const file = input.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('logoPreview').src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        }

        // 切换营业日
        function toggleDay(toggle) {
            const row = toggle.closest('.hour-row');
            const inputs = row.querySelectorAll('.time-input');
            
            if (toggle.classList.contains('active')) {
                toggle.classList.remove('active');
                inputs.forEach(input => {
                    input.disabled = true;
                    input.style.opacity = '0.5';
                });
            } else {
                toggle.classList.add('active');
                inputs.forEach(input => {
                    input.disabled = false;
                    input.style.opacity = '1';
                });
            }
        }

        // 处理文件选择
        document.querySelectorAll('input[type="file"]').forEach(input => {
            input.addEventListener('change', function(e) {
                const files = Array.from(e.target.files);
                const listId = e.target.id + 'List';
                const listContainer = document.getElementById(listId);
                
                listContainer.innerHTML = '';
                
                files.forEach((file, index) => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'file-item';
                    fileItem.innerHTML = `
                        <div class="file-info">
                            <i class="fas fa-file"></i>
                            <span>${file.name}</span>
                            <small>(${(file.size / 1024 / 1024).toFixed(2)} MB)</small>
                        </div>
                        <button type="button" onclick="removeFile('${e.target.id}', ${index})" style="background: none; border: none; color: #f44336; cursor: pointer;">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                    listContainer.appendChild(fileItem);
                });
            });
        });

        // 移除文件
        function removeFile(inputId, index) {
            const input = document.getElementById(inputId);
            const dt = new DataTransfer();
            const files = Array.from(input.files);
            
            files.forEach((file, i) => {
                if (i !== index) {
                    dt.items.add(file);
                }
            });
            
            input.files = dt.files;
            input.dispatchEvent(new Event('change'));
        }

        // 重置表单
        function resetForm() {
            if (confirm('确定要重置所有设置吗？')) {
                document.getElementById('storeInfoForm').reset();
                // Clear file lists
                document.getElementById('businessLicenseList').innerHTML = '';
                document.getElementById('legalIdCardList').innerHTML = '';
                document.getElementById('industryLicenseList').innerHTML = '';
                document.getElementById('logoPreview').src = '../../admin-backend/assets/images/product-default.jpg';
                showNotification('表单已重置', 'info');
            }
        }

        // 保存草稿
        function saveAsDraft() {
            const formData = new FormData(document.getElementById('storeInfoForm'));
            const draftData = {};
            
            for (let [key, value] of formData.entries()) {
                draftData[key] = value;
            }
            
            localStorage.setItem('storeInfoDraft', JSON.stringify(draftData));
            showNotification('草稿已保存', 'success');
        }

        // 表单提交
        document.getElementById('storeInfoForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalHTML = submitBtn.innerHTML;
            
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
            submitBtn.disabled = true;
            
            setTimeout(() => {
                submitBtn.innerHTML = originalHTML;
                submitBtn.disabled = false;
                showNotification('店铺信息已成功保存！', 'success');
                
                // 清除草稿
                localStorage.removeItem('storeInfoDraft');
            }, 2000);
        });

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
                color: white;
                padding: 16px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
                z-index: 1000;
                font-weight: 500;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // 页面加载时加载草稿
        document.addEventListener('DOMContentLoaded', function() {
            const draftData = localStorage.getItem('storeInfoDraft');
            if (draftData) {
                const data = JSON.parse(draftData);
                Object.keys(data).forEach(key => {
                    const input = document.querySelector(`[name="${key}"]`);
                    if (input && input.type !== 'file') {
                        input.value = data[key];
                    }
                });
                showNotification('已加载上次保存的草稿', 'info');
            }
        });
    </script>
</body>
</html> 